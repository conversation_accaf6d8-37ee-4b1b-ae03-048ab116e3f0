{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\payments\\backofficepaymentservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\payments\\backofficepaymentservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\payments\\models\\subscriptionfeeexportrecord.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\payments\\models\\subscriptionfeeexportrecord.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\paymentapi\\proxy\\paymentapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\paymentapi\\proxy\\paymentapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\creditapplicationscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\creditapplicationscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\onboardingapi\\onboardingapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\onboardingapi\\onboardingapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\drawapprovalsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\drawapprovalsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\drawapprovalscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\drawapprovalscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\generators\\payabledetailsgenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\generators\\payabledetailsgenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\drawapprovals\\details\\drawpayablesdetailsresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\drawapprovals\\details\\drawpayablesdetailsresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\nodeapi\\proxy\\nodeapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\nodeapi\\proxy\\nodeapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\node\\nodeservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\node\\nodeservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\messages\\models\\invoicepaymentrequestdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\messages\\models\\invoicepaymentrequestdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\messages\\models\\cardpaymentdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\messages\\models\\cardpaymentdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\admin\\adminservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\admin\\adminservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\constants\\routes.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\constants\\routes.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\paymentsadmincontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\paymentsadmincontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\payments\\models\\createmanualpaymentrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\payments\\models\\createmanualpaymentrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\payments\\models\\createcardpaymentrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\payments\\models\\createcardpaymentrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\paymentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\paymentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.domain\\extensions\\jsonextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|solutionrelative:bluetape.backoffice.decisionengine.domain\\extensions\\jsonextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\nodeapi\\httpclients\\nodehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\nodeapi\\httpclients\\nodehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\senders\\abstractions\\idrawrepaymentmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\senders\\abstractions\\idrawrepaymentmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\senders\\drawrepaymentmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\senders\\drawrepaymentmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{73535F87-77EC-417F-BB8B-4F9310CBD794}|..\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.dataaccess.external.tests\\nodeapi\\nodeapiproxytests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\di\\extensions\\apiextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\di\\extensions\\apiextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\accounts\\accountservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\accounts\\accountservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\accountscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\accountscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\common\\generators\\bankaccountsgenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\common\\generators\\bankaccountsgenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\accounts\\generators\\accountcreditdetailsgenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\accounts\\generators\\accountcreditdetailsgenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\backofficepaymentservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\constants\\flowtemplatecodes.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\constants\\flowtemplatecodes.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\drawapprovals\\drawapprovalresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\drawapprovals\\drawapprovalresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\drawapprovalnotescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\drawapprovalnotescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\drawscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\drawscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\draws\\drawservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\draws\\drawservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\extensions\\decisionengineextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\extensions\\decisionengineextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\generators\\approvaldetailsgenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\generators\\approvaldetailsgenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\mappings\\applicationprofile.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\mappings\\applicationprofile.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\models\\details\\drawdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\models\\details\\drawdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\models\\details\\drawapprovaldetailsrecord.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\models\\details\\drawapprovaldetailsrecord.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\bankaccountcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\bankaccountcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\companiescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\companiescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\creditapplicationnotescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\creditapplicationnotescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\creditscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\creditscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\middlewares\\exceptionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\middlewares\\exceptionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\loanapi\\proxy\\loanapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\loanapi\\proxy\\loanapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{73535F87-77EC-417F-BB8B-4F9310CBD794}|..\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.dataaccess.external.tests\\paymentapi\\paymentapiproxytests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\paymentapi\\httpclients\\paymenthttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\paymentapi\\httpclients\\paymenthttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\paymentapi\\proxy\\ipaymentapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\paymentapi\\proxy\\ipaymentapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\payments\\ibackofficepaymentservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\payments\\ibackofficepaymentservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\giactverificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\giactverificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\plaidapi\\proxy\\plaidapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\plaidapi\\proxy\\plaidapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\accountservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\plaidapi\\httpclients\\plaidhttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\plaidapi\\httpclients\\plaidhttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\plaidapi\\httpclients\\iplaidhttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\plaidapi\\httpclients\\iplaidhttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\plaidapi\\proxy\\iplaidapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\plaidapi\\proxy\\iplaidapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\generators\\paymentplandetailsgenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\generators\\paymentplandetailsgenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\loantemplatescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\loantemplatescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\loantemplateservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\loantemplates\\loantemplateservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\loantemplates\\loantemplateservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\mappings\\apiprofile.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\mappings\\apiprofile.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\draws\\models\\draw.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\draws\\models\\draw.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\nodeservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\node\\inodeservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\node\\inodeservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.domain\\constants\\httpheaders.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|solutionrelative:bluetape.backoffice.decisionengine.domain\\constants\\httpheaders.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\nodeapi\\constants\\nodeapiconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\nodeapi\\constants\\nodeapiconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\guestsupplierscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\guestsupplierscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\loanpricingpackagescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\loanpricingpackagescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\projectscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\projectscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\draws\\idrawservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\draws\\idrawservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\drawservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\reports\\reportfilegenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\reports\\reportfilegenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\loanapi\\builders\\pathbuilder.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\loanapi\\builders\\pathbuilder.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\generators\\drawdetailsgeneratortests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\models\\list\\drawapprovalrecord.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\models\\list\\drawapprovalrecord.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\models\\list\\drawapprovalrecorddetails.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\models\\list\\drawapprovalrecorddetails.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\drawapprovals\\details\\drawapprovaldetailsrecordresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\drawapprovals\\details\\drawapprovaldetailsrecordresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\loanapi\\httpclients\\loanhttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\loanapi\\httpclients\\loanhttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\07f3fef686984a8ead060ae9b5a237d43600\\2b\\e8d4fb4e\\CreatePaymentRequest.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\messages\\models\\drawrepaymentmanualdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\messages\\models\\drawrepaymentmanualdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\creditapplicationnotes\\creditapplicationnotesservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\creditapplicationnotes\\creditapplicationnotesservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\adminservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\bankaccounts\\bankaccountsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\bankaccounts\\bankaccountsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\appconfigurationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\appconfigurationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\creditapplicationnoteresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\creditapplicationnoteresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\creditapplicationnotesservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\guestsuppliers\\guestsupplierservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\guestsuppliers\\guestsupplierservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\drawapprovalnotes\\drawapprovalnotesservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\drawapprovalnotes\\drawapprovalnotesservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\companyapi\\icompanyapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\companyapi\\icompanyapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\creditapplicationsservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\creditapplicationdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\creditapplicationdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\onboardingapi\\onboardinghttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\onboardingapi\\onboardinghttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.domain\\extensions\\objectextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|solutionrelative:bluetape.backoffice.decisionengine.domain\\extensions\\objectextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\paymentapi\\httpclients\\ipaymenthttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\paymentapi\\httpclients\\ipaymenthttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\appsettings.prod.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\appsettings.prod.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{73535F87-77EC-417F-BB8B-4F9310CBD794}|..\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.dataaccess.external.tests\\companyapi\\companyapiproxytests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\accounts\\accountdetailedresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\accounts\\accountdetailedresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\accounts\\models\\accountdetailedmodel.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\accounts\\models\\accountdetailedmodel.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\extensions\\formattingextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\extensions\\formattingextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\accounts\\models\\accountcreditdetailsmodel.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\accounts\\models\\accountcreditdetailsmodel.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\accounts\\accountcreditdetailsresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\accounts\\accountcreditdetailsresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\accounts\\accountihcoveralldetailsresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\accounts\\accountihcoveralldetailsresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\creditapplicationlistresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\creditapplicationlistresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\controllers\\cardpricingpackagescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\controllers\\cardpricingpackagescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\constants\\decisionengine\\decisionsources.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\constants\\decisionengine\\decisionsources.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\generators\\accountdetailsgenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\generators\\accountdetailsgenerator.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\nodeapi\\httpclients\\inodehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\nodeapi\\httpclients\\inodehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\cardpricingpackages\\cardpricingpackageservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\cardpricingpackages\\cardpricingpackageservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\giactapi\\httpclients\\giactintegrationservicehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\giactapi\\httpclients\\giactintegrationservicehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\di\\constants\\keyvaultkeys.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\di\\constants\\keyvaultkeys.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\giactapi\\proxy\\giactintegrationapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\giactapi\\proxy\\giactintegrationapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\extensions\\configurationextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\extensions\\configurationextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.domain\\models\\aionaccountshort.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|solutionrelative:bluetape.backoffice.decisionengine.domain\\models\\aionaccountshort.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.domain\\models\\paginatedlist.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|solutionrelative:bluetape.backoffice.decisionengine.domain\\models\\paginatedlist.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\queries\\drawapprovalslistquery.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\queries\\drawapprovalslistquery.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\invoiceapi\\invoiceapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\invoiceapi\\invoiceapiproxy.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\projects\\projectservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\projects\\projectservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.domain\\enums\\ui\\creditapplicationsortingparameter.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|solutionrelative:bluetape.backoffice.decisionengine.domain\\enums\\ui\\creditapplicationsortingparameter.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\creditapplications\\icreditapplicationsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\creditapplications\\icreditapplicationsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\TFSTemp\\vctmp11904_365077.AccountService.1c1d0f5c.Current.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\creditapplicationrecord.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\creditapplicationrecord.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\creditapplicationslistfilter.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\creditapplicationslistfilter.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\accounts\\iaccountsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\accounts\\iaccountsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\deexecutions\\delastexecutionsdetailedresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\deexecutions\\delastexecutionsdetailedresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\deexecutions\\deexecutionpreliminaryresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\deexecutions\\deexecutionpreliminaryresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\decisionengineexecutions\\delastexecutionsdetailed.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\decisionengineexecutions\\delastexecutionsdetailed.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\decisionengineexecutions\\deexecutionbankdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\decisionengineexecutions\\deexecutionbankdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\creditapplicationservicetests.details.cashflow.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\checkingrevenueresult.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\checkingrevenueresult.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\cashflowitem.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\cashflowitem.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\queries\\creditapplicationslistquery.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\queries\\creditapplicationslistquery.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\draftservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\creditapplicationsservicetests.details.documentverification.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\globalusings.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{141D7109-C792-4649-8E22-CE0224B8CE21}|..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\tests\\bluetape.backoffice.decisionengine.application.tests\\services\\appconfigurationservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\bluetape.backoffice.decisionengine.api.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\bluetape.backoffice.decisionengine.api.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\loanapi\\constants\\loanserviceconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\loanapi\\constants\\loanserviceconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\requests\\reviewdrawapprovalrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\requests\\reviewdrawapprovalrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\error\\errordto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\error\\errordto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\loanapi\\httpclients\\iloanhttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\loanapi\\httpclients\\iloanhttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\models\\list\\drawapprovallistfilter.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\models\\list\\drawapprovallistfilter.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\creditapplicationdetailsmodel.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\creditapplications\\models\\creditapplicationdetailsmodel.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\creditapplicationdetailsresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\creditapplicationdetailsresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\dd6d519de5704567a9de36d0cb79346f14600\\43\\49c4c376\\UpdateCreditDetailsDto.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\credits\\creditresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\credits\\creditresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\accounts\\models\\list\\accountrecord.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\accounts\\models\\list\\accountrecord.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.domain\\enums\\ui\\accountsortingparameter.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{B0652FD4-B6A2-4210-AFA4-BA85BBFCF220}|BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj|solutionrelative:bluetape.backoffice.decisionengine.domain\\enums\\ui\\accountsortingparameter.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\constants\\creditapplicationfields.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\constants\\creditapplicationfields.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DecisionEngine\\DecisionEngineService.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:BlueTape.BackOffice.DecisionEngine.Application\\Services\\DecisionEngine\\DecisionEngineService.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\drawapprovals\\drawapprovalrecorddetailsresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\drawapprovals\\drawapprovalrecorddetailsresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\messages\\models\\manualpaymentdetails.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\messages\\models\\manualpaymentdetails.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.dataaccess.external\\nodeapi\\proxy\\inodeapiproxy.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{E08E4DE3-4E95-4336-98E7-0C0704B5108D}|BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.backoffice.decisionengine.dataaccess.external\\nodeapi\\proxy\\inodeapiproxy.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\models\\details\\drawpaymentplandetails.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C1E63A72-D0E1-419A-9222-1B74066CA95F}|BlueTape.BackOffice.DecisionEngine.Application\\BlueTape.BackOffice.DecisionEngine.Application.csproj|solutionrelative:bluetape.backoffice.decisionengine.application\\services\\drawapprovals\\models\\details\\drawpaymentplandetails.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\back-office-decision-engine\\src\\bluetape.backoffice.decisionengine.api\\models\\responses\\drawapprovals\\details\\drawpaymentplandetailsresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{580DF4A7-2E2D-439A-8A2E-52ECC426DA0F}|BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.csproj|solutionrelative:bluetape.backoffice.decisionengine.api\\models\\responses\\drawapprovals\\details\\drawpaymentplandetailsresponse.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Document", "DocumentIndex": 3, "Title": "CreditApplicationsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CreditApplicationsController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CreditApplicationsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CreditApplicationsController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CreditApplicationsController.cs", "ViewState": "AgIAABEAAAAAAAAAAAAcwCMAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-13T11:04:51.773Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Properties\\launchSettings.json", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Properties\\launchSettings.json", "ViewState": "AgIAABEAAAAAAAAAAABRwCMAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-10-09T15:26:04.177Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "DrawApprovalsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\DrawApprovalsController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\DrawApprovalsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\DrawApprovalsController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\DrawApprovalsController.cs", "ViewState": "AgIAAAwAAAAAAAAAAADgvyAAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-16T11:37:41.831Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "PaymentApiProxy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\Proxy\\PaymentApiProxy.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\Proxy\\PaymentApiProxy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\Proxy\\PaymentApiProxy.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\Proxy\\PaymentApiProxy.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAABsAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T21:13:37.785Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "SubscriptionFeeExportRecord.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\Models\\SubscriptionFeeExportRecord.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\Models\\SubscriptionFeeExportRecord.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\Models\\SubscriptionFeeExportRecord.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\Models\\SubscriptionFeeExportRecord.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-09T08:40:45.767Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "BackofficePaymentService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\BackofficePaymentService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\BackofficePaymentService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\BackofficePaymentService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\BackofficePaymentService.cs", "ViewState": "AgIAANwBAAAAAAAAAAAuwPkBAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T09:28:47.874Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "OnboardingApiProxy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\OnboardingApi\\OnboardingApiProxy.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\OnboardingApi\\OnboardingApiProxy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\OnboardingApi\\OnboardingApiProxy.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\OnboardingApi\\OnboardingApiProxy.cs", "ViewState": "AgIAACgAAAAAAAAAAAAmwEMAAACLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-24T13:43:10.107Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "DrawPayablesDetailsResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\Details\\DrawPayablesDetailsResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\Details\\DrawPayablesDetailsResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\Details\\DrawPayablesDetailsResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\Details\\DrawPayablesDetailsResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:21:32.79Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "DrawApprovalsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\DrawApprovalsService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\DrawApprovalsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\DrawApprovalsService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\DrawApprovalsService.cs", "ViewState": "AgIAAK8AAAAAAAAAAAAlwJIAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-16T11:38:14.102Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "PayableDetailsGenerator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\PayableDetailsGenerator.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\PayableDetailsGenerator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\PayableDetailsGenerator.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\PayableDetailsGenerator.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAkwEQAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:21:56.367Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "CreateCardPaymentRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\Models\\CreateCardPaymentRequest.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\Models\\CreateCardPaymentRequest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\Models\\CreateCardPaymentRequest.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\Models\\CreateCardPaymentRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T09:45:27.243Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Program.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Program.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Program.cs", "ViewState": "AgIAAFoAAAAAAAAAAAAAAHIAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-18T10:54:11.135Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "NodeService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Node\\NodeService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Node\\NodeService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Node\\NodeService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Node\\NodeService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-12T10:58:01.816Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "CreateManualPaymentRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\Models\\CreateManualPaymentRequest.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\Models\\CreateManualPaymentRequest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\Models\\CreateManualPaymentRequest.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\Models\\CreateManualPaymentRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T09:45:26.576Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "NodeApiProxy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\Proxy\\NodeApiProxy.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\Proxy\\NodeApiProxy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\Proxy\\NodeApiProxy.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\Proxy\\NodeApiProxy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAABkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T09:46:42.216Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "CardPaymentDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\CardPaymentDetails.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\CardPaymentDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\CardPaymentDetails.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\CardPaymentDetails.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T09:28:42.487Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "InvoicePaymentRequestDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\InvoicePaymentRequestDetails.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\InvoicePaymentRequestDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\InvoicePaymentRequestDetails.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\InvoicePaymentRequestDetails.cs", "ViewState": "AgIAABAAAAAAAAAAAAAswCEAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T09:28:45.589Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "AdminController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\AdminController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\AdminController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\AdminController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\AdminController.cs", "ViewState": "AgIAAOIAAAAAAAAAAAAWwPoAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-18T10:53:39.312Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "PaymentsAdminController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\PaymentsAdminController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\PaymentsAdminController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\PaymentsAdminController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\PaymentsAdminController.cs", "ViewState": "AgIAAFYAAAAAAAAAAAAhwGgAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-10T14:06:25.891Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "Routes.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Constants\\Routes.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Constants\\Routes.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Constants\\Routes.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Constants\\Routes.cs", "ViewState": "AgIAABMAAAAAAAAAAADwvyUAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-18T10:32:51.35Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "AdminService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Admin\\AdminService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Admin\\AdminService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Admin\\AdminService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Admin\\AdminService.cs", "ViewState": "AgIAAPYAAAAAAAAAAAArwA0BAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-18T09:34:54.158Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "PaymentsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\PaymentsController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\PaymentsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\PaymentsController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\PaymentsController.cs", "ViewState": "AgIAAEAAAAAAAAAAAAAlwC0AAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-10T15:09:16.451Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "JsonExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Extensions\\JsonExtensions.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Domain\\Extensions\\JsonExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Extensions\\JsonExtensions.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Domain\\Extensions\\JsonExtensions.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAQwAgAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T10:56:07.203Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "NodeHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\HttpClients\\NodeHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\HttpClients\\NodeHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\HttpClients\\NodeHttpClient.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\HttpClients\\NodeHttpClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwA4AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-12T10:57:42.804Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "IDrawRepaymentMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Senders\\Abstractions\\IDrawRepaymentMessageSender.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Senders\\Abstractions\\IDrawRepaymentMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Senders\\Abstractions\\IDrawRepaymentMessageSender.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Senders\\Abstractions\\IDrawRepaymentMessageSender.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T08:14:03.84Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "DrawRepaymentMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Senders\\DrawRepaymentMessageSender.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Senders\\DrawRepaymentMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Senders\\DrawRepaymentMessageSender.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Senders\\DrawRepaymentMessageSender.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T08:07:59.242Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "ApiExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\DI\\Extensions\\ApiExtensions.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\DI\\Extensions\\ApiExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\DI\\Extensions\\ApiExtensions.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\DI\\Extensions\\ApiExtensions.cs", "ViewState": "AgIAAE4AAAAAAAAAAIBJwGQAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-23T08:44:12.047Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "NodeApiProxyTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\NodeApi\\NodeApiProxyTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\NodeApi\\NodeApiProxyTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\NodeApi\\NodeApiProxyTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\NodeApi\\NodeApiProxyTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T13:16:27.733Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "AccountService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\AccountService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\AccountService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\AccountService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\AccountService.cs", "ViewState": "AgIAAL4AAAAAAAAAAAAvwKUAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T16:05:13.452Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "AccountsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\AccountsController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\AccountsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\AccountsController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\AccountsController.cs", "ViewState": "AgIAADcAAAAAAAAAAAAswEkAAABSAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-18T10:53:33.582Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "BankAccountsGenerator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Common\\Generators\\BankAccountsGenerator.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Common\\Generators\\BankAccountsGenerator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Common\\Generators\\BankAccountsGenerator.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Common\\Generators\\BankAccountsGenerator.cs", "ViewState": "AgIAADMAAAAAAAAAAAAnwEcAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T11:59:20.301Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "AccountCreditDetailsGenerator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Generators\\AccountCreditDetailsGenerator.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Generators\\AccountCreditDetailsGenerator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Generators\\AccountCreditDetailsGenerator.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Generators\\AccountCreditDetailsGenerator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T17:28:17.866Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "BackofficePaymentServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\BackofficePaymentServiceTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\BackofficePaymentServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\BackofficePaymentServiceTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\BackofficePaymentServiceTests.cs", "ViewState": "AgIAALkDAAAAAAAAAAAIwMcDAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-29T15:06:11.111Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "FlowTemplateCodes.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Constants\\FlowTemplateCodes.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Constants\\FlowTemplateCodes.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Constants\\FlowTemplateCodes.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Constants\\FlowTemplateCodes.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:51:33.993Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "DrawService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Draws\\DrawService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Draws\\DrawService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Draws\\DrawService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Draws\\DrawService.cs", "ViewState": "AgIAAAoBAAAAAAAAAAAiwBsAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-18T09:32:23.91Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "DrawApprovalResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\DrawApprovalResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\DrawApprovalResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\DrawApprovalResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\DrawApprovalResponse.cs", "ViewState": "AgIAABcAAAAAAAAAAAA9wCIAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T08:05:34.447Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "DecisionEngineExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Extensions\\DecisionEngineExtensions.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Extensions\\DecisionEngineExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Extensions\\DecisionEngineExtensions.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Extensions\\DecisionEngineExtensions.cs", "ViewState": "AgIAAIcAAAAAAAAAAAAIwJIAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-29T14:52:42.587Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "DrawApprovalNotesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\DrawApprovalNotesController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\DrawApprovalNotesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\DrawApprovalNotesController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\DrawApprovalNotesController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-13T11:04:50.077Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "DrawsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\DrawsController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\DrawsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\DrawsController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\DrawsController.cs", "ViewState": "AgIAABgAAAAAAAAAAAAqwCQAAACNAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-27T06:57:56.914Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "ApprovalDetailsGenerator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\ApprovalDetailsGenerator.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\ApprovalDetailsGenerator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\ApprovalDetailsGenerator.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\ApprovalDetailsGenerator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T14:45:47.924Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "ApplicationProfile.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Mappings\\ApplicationProfile.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Mappings\\ApplicationProfile.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Mappings\\ApplicationProfile.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Mappings\\ApplicationProfile.cs", "ViewState": "AgIAAEIAAAAAAAAAAAAwwFYAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T13:37:13.172Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "DrawApprovalDetailsRecord.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\Details\\DrawApprovalDetailsRecord.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\Details\\DrawApprovalDetailsRecord.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\Details\\DrawApprovalDetailsRecord.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\Details\\DrawApprovalDetailsRecord.cs", "ViewState": "AgIAACYAAAAAAAAAAAAkwCcAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T13:36:46.115Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "BankAccountController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\BankAccountController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\BankAccountController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\BankAccountController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\BankAccountController.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAWwEEAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-27T15:46:12.324Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "CompaniesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CompaniesController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CompaniesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CompaniesController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CompaniesController.cs", "ViewState": "AgIAABEAAAAAAAAAAAAQwCIAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T12:20:37.722Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "DrawDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\Details\\DrawDetails.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\Details\\DrawDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\Details\\DrawDetails.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\Details\\DrawDetails.cs", "ViewState": "AgIAAAwAAAAAAAAAAADwvxkAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T14:45:20.947Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "CreditApplicationNotesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CreditApplicationNotesController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CreditApplicationNotesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CreditApplicationNotesController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CreditApplicationNotesController.cs", "ViewState": "AgIAAFoAAAAAAAAAAAAwwDAAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-13T11:04:52.283Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "CreditsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CreditsController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CreditsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CreditsController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CreditsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T17:02:07.605Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "PaymentHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\HttpClients\\PaymentHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\HttpClients\\PaymentHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\HttpClients\\PaymentHttpClient.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\HttpClients\\PaymentHttpClient.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAYwBIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-26T11:34:12.734Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "ExceptionMiddleware.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Middlewares\\ExceptionMiddleware.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Middlewares\\ExceptionMiddleware.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Middlewares\\ExceptionMiddleware.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Middlewares\\ExceptionMiddleware.cs", "ViewState": "AgIAABwAAAAAAAAAAAAcwCYAAABjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T11:26:12.834Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "LoanApiProxy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\Proxy\\LoanApiProxy.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\Proxy\\LoanApiProxy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\Proxy\\LoanApiProxy.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\Proxy\\LoanApiProxy.cs", "ViewState": "AgIAAOYAAAAAAAAAAAAwwPcAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T08:54:27.982Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "PaymentApiProxyTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\PaymentApi\\PaymentApiProxyTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\PaymentApi\\PaymentApiProxyTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\PaymentApi\\PaymentApiProxyTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\PaymentApi\\PaymentApiProxyTests.cs", "ViewState": "AgIAACYAAAAAAAAAAAAvwCsAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-24T13:42:31.544Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "IPaymentApiProxy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\Proxy\\IPaymentApiProxy.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\Proxy\\IPaymentApiProxy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\Proxy\\IPaymentApiProxy.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\Proxy\\IPaymentApiProxy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-30T08:50:10.612Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "IBackofficePaymentService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\IBackofficePaymentService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\IBackofficePaymentService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\IBackofficePaymentService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Payments\\IBackofficePaymentService.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAswBIAAABuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-29T11:26:53.722Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "AccountServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\AccountServiceTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\AccountServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\AccountServiceTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\AccountServiceTests.cs", "ViewState": "AgIAAHAAAAAAAAAAAAAwwIIAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-10T15:07:15.658Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "GiactVerificationController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\GiactVerificationController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\GiactVerificationController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\GiactVerificationController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\GiactVerificationController.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBZwB4AAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T15:49:13.271Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "PlaidApiProxy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\Proxy\\PlaidApiProxy.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\Proxy\\PlaidApiProxy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\Proxy\\PlaidApiProxy.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\Proxy\\PlaidApiProxy.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBZwBYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T10:36:38.471Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "IPlaidApiProxy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\Proxy\\IPlaidApiProxy.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\Proxy\\IPlaidApiProxy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\Proxy\\IPlaidApiProxy.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\Proxy\\IPlaidApiProxy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T13:59:19.196Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "PlaidHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\HttpClients\\PlaidHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\HttpClients\\PlaidHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\HttpClients\\PlaidHttpClient.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\HttpClients\\PlaidHttpClient.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAmwBgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T14:23:48.748Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "IPlaidHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\HttpClients\\IPlaidHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\HttpClients\\IPlaidHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\HttpClients\\IPlaidHttpClient.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PlaidApi\\HttpClients\\IPlaidHttpClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T14:32:33.578Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "LoanTemplatesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\LoanTemplatesController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\LoanTemplatesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\LoanTemplatesController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\LoanTemplatesController.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAlwBcAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-10T13:52:48.392Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAAwBYAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-27T09:38:19.786Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "LoanTemplateService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\LoanTemplates\\LoanTemplateService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\LoanTemplates\\LoanTemplateService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\LoanTemplates\\LoanTemplateService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\LoanTemplates\\LoanTemplateService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-21T08:09:28.228Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "LoanTemplateServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\LoanTemplateServiceTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\LoanTemplateServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\LoanTemplateServiceTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\LoanTemplateServiceTests.cs", "ViewState": "AgIAAAgAAAAAAAAAAAD4vxsAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-21T08:09:39.09Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "PaymentPlanDetailsGenerator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\PaymentPlanDetailsGenerator.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\PaymentPlanDetailsGenerator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\PaymentPlanDetailsGenerator.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\PaymentPlanDetailsGenerator.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAawB0AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-21T07:43:12.234Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "ApiProfile.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Mappings\\ApiProfile.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Mappings\\ApiProfile.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Mappings\\ApiProfile.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Mappings\\ApiProfile.cs", "ViewState": "AgIAAJIAAAAAAAAAAAAAwJsAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-11T07:55:31.914Z"}, {"$type": "Document", "DocumentIndex": 158, "Title": "DrawPaymentPlanDetailsResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\Details\\DrawPaymentPlanDetailsResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\Details\\DrawPaymentPlanDetailsResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\Details\\DrawPaymentPlanDetailsResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\Details\\DrawPaymentPlanDetailsResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-21T07:50:59.872Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "Draw.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Draws\\Models\\Draw.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Draws\\Models\\Draw.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Draws\\Models\\Draw.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Draws\\Models\\Draw.cs", "ViewState": "AgIAABIAAAAAAAAAAAAzwBsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T12:45:35.781Z"}, {"$type": "Document", "DocumentIndex": 157, "Title": "DrawPaymentPlanDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\Details\\DrawPaymentPlanDetails.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\Details\\DrawPaymentPlanDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\Details\\DrawPaymentPlanDetails.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\Details\\DrawPaymentPlanDetails.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-21T07:43:28.475Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "NodeServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\NodeServiceTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\NodeServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\NodeServiceTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\NodeServiceTests.cs", "ViewState": "AgIAABcAAAAAAAAAAADwvyMAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:25:44.359Z"}, {"$type": "Document", "DocumentIndex": 156, "Title": "INodeApiProxy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\Proxy\\INodeApiProxy.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\Proxy\\INodeApiProxy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\Proxy\\INodeApiProxy.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\Proxy\\INodeApiProxy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:23:43.719Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "INodeService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Node\\INodeService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Node\\INodeService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Node\\INodeService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Node\\INodeService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:13:53.287Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "HttpHeaders.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Constants\\HttpHeaders.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Domain\\Constants\\HttpHeaders.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Constants\\HttpHeaders.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Domain\\Constants\\HttpHeaders.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T12:43:00.923Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "NodeApiConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\Constants\\NodeApiConstants.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\Constants\\NodeApiConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\Constants\\NodeApiConstants.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\Constants\\NodeApiConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T11:31:06.539Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "ProjectsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\ProjectsController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\ProjectsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\ProjectsController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\ProjectsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBpwBwAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-27T08:59:46.9Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "LoanPricingPackagesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\LoanPricingPackagesController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\LoanPricingPackagesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\LoanPricingPackagesController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\LoanPricingPackagesController.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBpwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-01T12:23:09.117Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "GuestSuppliersController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\GuestSuppliersController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\GuestSuppliersController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\GuestSuppliersController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\GuestSuppliersController.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBZwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-27T15:47:19.529Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "IDrawService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Draws\\IDrawService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Draws\\IDrawService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Draws\\IDrawService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Draws\\IDrawService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAcAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-27T06:57:48.131Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "DrawServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\DrawServiceTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\DrawServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\DrawServiceTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\DrawServiceTests.cs", "ViewState": "AgIAABEAAAAAAAAAAAAqwBoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-18T09:32:25.638Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "DrawDetailsGeneratorTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\Generators\\DrawDetailsGeneratorTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\Generators\\DrawDetailsGeneratorTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\Generators\\DrawDetailsGeneratorTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\Generators\\DrawDetailsGeneratorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBJwBcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-27T09:57:11.363Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "ReportFileGenerator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Reports\\ReportFileGenerator.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Reports\\ReportFileGenerator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Reports\\ReportFileGenerator.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Reports\\ReportFileGenerator.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAiwAwAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T17:47:23.006Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "PathBuilder.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\Builders\\PathBuilder.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\Builders\\PathBuilder.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\Builders\\PathBuilder.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\Builders\\PathBuilder.cs", "ViewState": "AgIAABgAAAAAAAAAAAAcwBwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T08:02:57.616Z"}, {"$type": "Document", "DocumentIndex": 79, "Title": "DrawApprovalRecordDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\List\\DrawApprovalRecordDetails.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\List\\DrawApprovalRecordDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\List\\DrawApprovalRecordDetails.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\List\\DrawApprovalRecordDetails.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-27T09:33:00.373Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "DrawApprovalRecord.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\List\\DrawApprovalRecord.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\List\\DrawApprovalRecord.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\List\\DrawApprovalRecord.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\List\\DrawApprovalRecord.cs", "ViewState": "AgIAAB8AAAAAAAAAAIAxwCUAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-27T09:32:50.39Z"}, {"$type": "Document", "DocumentIndex": 80, "Title": "DrawApprovalDetailsRecordResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\Details\\DrawApprovalDetailsRecordResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\Details\\DrawApprovalDetailsRecordResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\Details\\DrawApprovalDetailsRecordResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\Details\\DrawApprovalDetailsRecordResponse.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAxwAwAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T08:05:29.685Z"}, {"$type": "Document", "DocumentIndex": 81, "Title": "LoanHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\HttpClients\\LoanHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\HttpClients\\LoanHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\HttpClients\\LoanHttpClient.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\HttpClients\\LoanHttpClient.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAYwAwAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-18T09:29:06.582Z"}, {"$type": "Document", "DocumentIndex": 82, "Title": "CreatePaymentRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\07f3fef686984a8ead060ae9b5a237d43600\\2b\\e8d4fb4e\\CreatePaymentRequest.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\07f3fef686984a8ead060ae9b5a237d43600\\2b\\e8d4fb4e\\CreatePaymentRequest.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\07f3fef686984a8ead060ae9b5a237d43600\\2b\\e8d4fb4e\\CreatePaymentRequest.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\07f3fef686984a8ead060ae9b5a237d43600\\2b\\e8d4fb4e\\CreatePaymentRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-07T13:22:28.747Z"}, {"$type": "Document", "DocumentIndex": 83, "Title": "DrawRepaymentManualDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\DrawRepaymentManualDetails.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\DrawRepaymentManualDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\DrawRepaymentManualDetails.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\DrawRepaymentManualDetails.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAjwBsAAABJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-30T09:15:41.624Z"}, {"$type": "Document", "DocumentIndex": 84, "Title": "CreditApplicationNotesService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplicationNotes\\CreditApplicationNotesService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplicationNotes\\CreditApplicationNotesService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplicationNotes\\CreditApplicationNotesService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplicationNotes\\CreditApplicationNotesService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-30T09:14:14.005Z"}, {"$type": "Document", "DocumentIndex": 155, "Title": "ManualPaymentDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\ManualPaymentDetails.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\ManualPaymentDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\ManualPaymentDetails.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Messages\\Models\\ManualPaymentDetails.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-29T14:56:32.956Z"}, {"$type": "Document", "DocumentIndex": 85, "Title": "AdminServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\AdminServiceTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\AdminServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\AdminServiceTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\AdminServiceTests.cs", "ViewState": "AgIAAEADAAAAAAAAAAAmwEUDAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-10T15:07:15.1Z"}, {"$type": "Document", "DocumentIndex": 87, "Title": "AppConfigurationController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\AppConfigurationController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\AppConfigurationController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\AppConfigurationController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\AppConfigurationController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-24T13:56:58.188Z"}, {"$type": "Document", "DocumentIndex": 86, "Title": "BankAccountsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\BankAccounts\\BankAccountsService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\BankAccounts\\BankAccountsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\BankAccounts\\BankAccountsService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\BankAccounts\\BankAccountsService.cs", "ViewState": "AgIAAAoAAAAAAAAAAAApwBYAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-14T07:31:27.914Z"}, {"$type": "Document", "DocumentIndex": 88, "Title": "CreditApplicationNoteResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\CreditApplicationNoteResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\CreditApplicationNoteResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\CreditApplicationNoteResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\CreditApplicationNoteResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-24T13:42:07.347Z"}, {"$type": "Document", "DocumentIndex": 89, "Title": "CreditApplicationNotesServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationNotesServiceTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationNotesServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationNotesServiceTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationNotesServiceTests.cs", "ViewState": "AgIAADEAAAAAAAAAAAAtwEEAAABOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-24T13:42:25.495Z"}, {"$type": "Document", "DocumentIndex": 90, "Title": "GuestSupplierService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\GuestSuppliers\\GuestSupplierService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\GuestSuppliers\\GuestSupplierService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\GuestSuppliers\\GuestSupplierService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\GuestSuppliers\\GuestSupplierService.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAtwB0AAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-24T13:42:22.878Z"}, {"$type": "Document", "DocumentIndex": 91, "Title": "DrawApprovalNotesService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovalNotes\\DrawApprovalNotesService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovalNotes\\DrawApprovalNotesService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovalNotes\\DrawApprovalNotesService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovalNotes\\DrawApprovalNotesService.cs", "ViewState": "AgIAACkAAAAAAAAAAAAawDcAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-24T13:42:15.623Z"}, {"$type": "Document", "DocumentIndex": 92, "Title": "ICompanyApiProxy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\CompanyApi\\ICompanyApiProxy.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\CompanyApi\\ICompanyApiProxy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\CompanyApi\\ICompanyApiProxy.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\CompanyApi\\ICompanyApiProxy.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAjwBIAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-08T08:50:01.855Z"}, {"$type": "Document", "DocumentIndex": 93, "Title": "CreditApplicationsServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationsServiceTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationsServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationsServiceTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationsServiceTests.cs", "ViewState": "AgIAACoAAAAAAAAAAAAuwD4AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-10T12:58:01.598Z"}, {"$type": "Document", "DocumentIndex": 94, "Title": "CreditApplicationDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationDetails.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationDetails.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationDetails.cs", "ViewState": "AgIAABEAAAAAAAAAAAAxwBgAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-18T16:44:06.976Z"}, {"$type": "Document", "DocumentIndex": 95, "Title": "OnboardingHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\OnboardingApi\\OnboardingHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\OnboardingApi\\OnboardingHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\OnboardingApi\\OnboardingHttpClient.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\OnboardingApi\\OnboardingHttpClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-27T09:25:45.898Z"}, {"$type": "Document", "DocumentIndex": 96, "Title": "ObjectExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Extensions\\ObjectExtensions.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Domain\\Extensions\\ObjectExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Extensions\\ObjectExtensions.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Domain\\Extensions\\ObjectExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-10T09:02:22.687Z"}, {"$type": "Document", "DocumentIndex": 97, "Title": "IPaymentHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\HttpClients\\IPaymentHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\HttpClients\\IPaymentHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\HttpClients\\IPaymentHttpClient.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\PaymentApi\\HttpClients\\IPaymentHttpClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-26T11:34:15.71Z"}, {"$type": "Document", "DocumentIndex": 98, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\appsettings.json", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\appsettings.json", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-04-08T20:59:40.622Z"}, {"$type": "Document", "DocumentIndex": 99, "Title": "appsettings.prod.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\appsettings.prod.json", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\appsettings.prod.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\appsettings.prod.json", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\appsettings.prod.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-04-08T20:59:44.057Z"}, {"$type": "Document", "DocumentIndex": 100, "Title": "CompanyApiProxyTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\CompanyApi\\CompanyApiProxyTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\CompanyApi\\CompanyApiProxyTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\CompanyApi\\CompanyApiProxyTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\CompanyApi\\CompanyApiProxyTests.cs", "ViewState": "AgIAANYAAAAAAAAAAADwv+YAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T16:05:35.131Z"}, {"$type": "Document", "DocumentIndex": 101, "Title": "AccountDetailedResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Accounts\\AccountDetailedResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Accounts\\AccountDetailedResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Accounts\\AccountDetailedResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Accounts\\AccountDetailedResponse.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAxwBcAAABxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-08T08:43:48.341Z"}, {"$type": "Document", "DocumentIndex": 103, "Title": "FormattingExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Extensions\\FormattingExtensions.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Extensions\\FormattingExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Extensions\\FormattingExtensions.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Extensions\\FormattingExtensions.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAAwCIAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-25T11:31:20.375Z"}, {"$type": "Document", "DocumentIndex": 102, "Title": "AccountDetailedModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Models\\AccountDetailedModel.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Models\\AccountDetailedModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Models\\AccountDetailedModel.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Models\\AccountDetailedModel.cs", "ViewState": "AgIAABMAAAAAAAAAAAAxwBwAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-08T09:56:10.981Z"}, {"$type": "Document", "DocumentIndex": 104, "Title": "AccountCreditDetailsModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Models\\AccountCreditDetailsModel.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Models\\AccountCreditDetailsModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Models\\AccountCreditDetailsModel.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Models\\AccountCreditDetailsModel.cs", "ViewState": "AgIAAA4AAAAAAAAAAIAxwBYAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T17:28:12.379Z"}, {"$type": "Document", "DocumentIndex": 105, "Title": "AccountCreditDetailsResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Accounts\\AccountCreditDetailsResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Accounts\\AccountCreditDetailsResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Accounts\\AccountCreditDetailsResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Accounts\\AccountCreditDetailsResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T17:32:54.13Z"}, {"$type": "Document", "DocumentIndex": 106, "Title": "AccountIhcOverAllDetailsResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Accounts\\AccountIhcOverAllDetailsResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Accounts\\AccountIhcOverAllDetailsResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Accounts\\AccountIhcOverAllDetailsResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Accounts\\AccountIhcOverAllDetailsResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-19T08:48:03.136Z"}, {"$type": "Document", "DocumentIndex": 107, "Title": "CreditApplicationListResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\CreditApplicationListResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\CreditApplicationListResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\CreditApplicationListResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\CreditApplicationListResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAmwBQAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T15:23:33.807Z"}, {"$type": "Document", "DocumentIndex": 108, "Title": "CardPricingPackagesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CardPricingPackagesController.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CardPricingPackagesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CardPricingPackagesController.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Controllers\\CardPricingPackagesController.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAqwCMAAABVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-19T08:47:09.541Z"}, {"$type": "Document", "DocumentIndex": 109, "Title": "DecisionSources.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Constants\\DecisionEngine\\DecisionSources.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Constants\\DecisionEngine\\DecisionSources.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Constants\\DecisionEngine\\DecisionSources.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Constants\\DecisionEngine\\DecisionSources.cs", "ViewState": "AgIAADQAAAAAAAAAAAAawEMAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-01T12:19:42.892Z"}, {"$type": "Document", "DocumentIndex": 110, "Title": "AccountDetailsGenerator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\AccountDetailsGenerator.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\AccountDetailsGenerator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\AccountDetailsGenerator.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Generators\\AccountDetailsGenerator.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAiwBgAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-20T06:46:13.945Z"}, {"$type": "Document", "DocumentIndex": 111, "Title": "INodeHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\HttpClients\\INodeHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\HttpClients\\INodeHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\HttpClients\\INodeHttpClient.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\NodeApi\\HttpClients\\INodeHttpClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-12T10:57:45.653Z"}, {"$type": "Document", "DocumentIndex": 112, "Title": "CardPricingPackageService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CardPricingPackages\\CardPricingPackageService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CardPricingPackages\\CardPricingPackageService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CardPricingPackages\\CardPricingPackageService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CardPricingPackages\\CardPricingPackageService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-10T19:48:31.673Z"}, {"$type": "Document", "DocumentIndex": 154, "Title": "DrawApprovalRecordDetailsResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\DrawApprovalRecordDetailsResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\DrawApprovalRecordDetailsResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\DrawApprovalRecordDetailsResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DrawApprovals\\DrawApprovalRecordDetailsResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-27T10:13:41.196Z"}, {"$type": "Document", "DocumentIndex": 113, "Title": "GiactIntegrationServiceHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\GiactApi\\HttpClients\\GiactIntegrationServiceHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\GiactApi\\HttpClients\\GiactIntegrationServiceHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\GiactApi\\HttpClients\\GiactIntegrationServiceHttpClient.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\GiactApi\\HttpClients\\GiactIntegrationServiceHttpClient.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAgwBMAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T17:01:09.34Z"}, {"$type": "Document", "DocumentIndex": 114, "Title": "KeyVaultKeys.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\DI\\Constants\\KeyVaultKeys.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\DI\\Constants\\KeyVaultKeys.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\DI\\Constants\\KeyVaultKeys.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\DI\\Constants\\KeyVaultKeys.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAvwBQAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T17:00:51.166Z"}, {"$type": "Document", "DocumentIndex": 115, "Title": "GiactIntegrationApiProxy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\GiactApi\\Proxy\\GiactIntegrationApiProxy.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\GiactApi\\Proxy\\GiactIntegrationApiProxy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\GiactApi\\Proxy\\GiactIntegrationApiProxy.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\GiactApi\\Proxy\\GiactIntegrationApiProxy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T15:50:27.236Z"}, {"$type": "Document", "DocumentIndex": 116, "Title": "ConfigurationExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Extensions\\ConfigurationExtensions.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Extensions\\ConfigurationExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Extensions\\ConfigurationExtensions.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Extensions\\ConfigurationExtensions.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAQwDQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-18T10:50:22.511Z"}, {"$type": "Document", "DocumentIndex": 117, "Title": "AionAccountShort.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Models\\AionAccountShort.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Domain\\Models\\AionAccountShort.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Models\\AionAccountShort.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Domain\\Models\\AionAccountShort.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-10T14:30:42.235Z"}, {"$type": "Document", "DocumentIndex": 118, "Title": "PaginatedList.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Models\\PaginatedList.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Domain\\Models\\PaginatedList.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Models\\PaginatedList.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Domain\\Models\\PaginatedList.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-10T15:21:34.17Z"}, {"$type": "Document", "DocumentIndex": 119, "Title": "DrawApprovalsListQuery.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Queries\\DrawApprovalsListQuery.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Queries\\DrawApprovalsListQuery.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Queries\\DrawApprovalsListQuery.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Queries\\DrawApprovalsListQuery.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAxwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T08:05:53.727Z"}, {"$type": "Document", "DocumentIndex": 120, "Title": "InvoiceApiProxy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\InvoiceApi\\InvoiceApiProxy.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\InvoiceApi\\InvoiceApiProxy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\InvoiceApi\\InvoiceApiProxy.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\InvoiceApi\\InvoiceApiProxy.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAIwBUAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-27T09:24:27.844Z"}, {"$type": "Document", "DocumentIndex": 121, "Title": "ProjectService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Projects\\ProjectService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Projects\\ProjectService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Projects\\ProjectService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Projects\\ProjectService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-27T09:05:49.273Z"}, {"$type": "Document", "DocumentIndex": 152, "Title": "CreditApplicationFields.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Constants\\CreditApplicationFields.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Constants\\CreditApplicationFields.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Constants\\CreditApplicationFields.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Constants\\CreditApplicationFields.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T21:06:25.85Z"}, {"$type": "Document", "DocumentIndex": 123, "Title": "ICreditApplicationsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\ICreditApplicationsService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\ICreditApplicationsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\ICreditApplicationsService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\ICreditApplicationsService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T20:50:22.575Z"}, {"$type": "Document", "DocumentIndex": 122, "Title": "CreditApplicationSortingParameter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Enums\\UI\\CreditApplicationSortingParameter.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Domain\\Enums\\UI\\CreditApplicationSortingParameter.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Enums\\UI\\CreditApplicationSortingParameter.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Domain\\Enums\\UI\\CreditApplicationSortingParameter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T21:07:15.875Z"}, {"$type": "Document", "DocumentIndex": 151, "Title": "AccountSortingParameter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Enums\\UI\\AccountSortingParameter.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Domain\\Enums\\UI\\AccountSortingParameter.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\Enums\\UI\\AccountSortingParameter.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Domain\\Enums\\UI\\AccountSortingParameter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T20:41:14.034Z"}, {"$type": "Document", "DocumentIndex": 150, "Title": "AccountRecord.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Models\\List\\AccountRecord.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Models\\List\\AccountRecord.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Models\\List\\AccountRecord.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\Models\\List\\AccountRecord.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T17:02:33.901Z"}, {"$type": "Document", "DocumentIndex": 149, "Title": "CreditResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Credits\\CreditResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Credits\\CreditResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Credits\\CreditResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\Credits\\CreditResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T15:51:45.352Z"}, {"$type": "Document", "DocumentIndex": 148, "Title": "UpdateCreditDetailsDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\dd6d519de5704567a9de36d0cb79346f14600\\43\\49c4c376\\UpdateCreditDetailsDto.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\dd6d519de5704567a9de36d0cb79346f14600\\43\\49c4c376\\UpdateCreditDetailsDto.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\dd6d519de5704567a9de36d0cb79346f14600\\43\\49c4c376\\UpdateCreditDetailsDto.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\dd6d519de5704567a9de36d0cb79346f14600\\43\\49c4c376\\UpdateCreditDetailsDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T14:38:08.639Z"}, {"$type": "Document", "DocumentIndex": 124, "Title": "vctmp11904_365077.AccountService.1c1d0f5c.Current.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\TFSTemp\\vctmp11904_365077.AccountService.1c1d0f5c.Current.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\TFSTemp\\vctmp11904_365077.AccountService.1c1d0f5c.Current.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\TFSTemp\\vctmp11904_365077.AccountService.1c1d0f5c.Current.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\TFSTemp\\vctmp11904_365077.AccountService.1c1d0f5c.Current.cs", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T09:01:57.918Z"}, {"$type": "Document", "DocumentIndex": 146, "Title": "CreditApplicationDetailsModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationDetailsModel.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationDetailsModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationDetailsModel.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationDetailsModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-19T15:36:30.311Z"}, {"$type": "Document", "DocumentIndex": 147, "Title": "CreditApplicationDetailsResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\CreditApplicationDetailsResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\CreditApplicationDetailsResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\CreditApplicationDetailsResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\CreditApplicationDetailsResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-19T15:37:01.718Z"}, {"$type": "Document", "DocumentIndex": 125, "Title": "CreditApplicationRecord.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationRecord.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationRecord.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationRecord.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationRecord.cs", "ViewState": "AgIAABkAAAAAAAAAAAAxwBsAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-18T16:42:15.12Z"}, {"$type": "Document", "DocumentIndex": 126, "Title": "CreditApplicationsListFilter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationsListFilter.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationsListFilter.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationsListFilter.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CreditApplicationsListFilter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-10T11:59:14.383Z"}, {"$type": "Document", "DocumentIndex": 127, "Title": "IAccountsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\IAccountsService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\IAccountsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\IAccountsService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\Accounts\\IAccountsService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-17T13:36:24.064Z"}, {"$type": "Document", "DocumentIndex": 128, "Title": "DeLastExecutionsDetailedResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DeExecutions\\DeLastExecutionsDetailedResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DeExecutions\\DeLastExecutionsDetailedResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DeExecutions\\DeLastExecutionsDetailedResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DeExecutions\\DeLastExecutionsDetailedResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-13T08:44:45.489Z"}, {"$type": "Document", "DocumentIndex": 130, "Title": "DeLastExecutionsDetailed.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\DecisionEngineExecutions\\DeLastExecutionsDetailed.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\DecisionEngineExecutions\\DeLastExecutionsDetailed.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\DecisionEngineExecutions\\DeLastExecutionsDetailed.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\DecisionEngineExecutions\\DeLastExecutionsDetailed.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-13T08:35:02.569Z"}, {"$type": "Document", "DocumentIndex": 129, "Title": "DeExecutionPreliminaryResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DeExecutions\\DeExecutionPreliminaryResponse.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DeExecutions\\DeExecutionPreliminaryResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DeExecutions\\DeExecutionPreliminaryResponse.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Responses\\DeExecutions\\DeExecutionPreliminaryResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-13T08:41:30.792Z"}, {"$type": "Document", "DocumentIndex": 131, "Title": "DeExecutionBankDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\DecisionEngineExecutions\\DeExecutionBankDetails.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\DecisionEngineExecutions\\DeExecutionBankDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\DecisionEngineExecutions\\DeExecutionBankDetails.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\DecisionEngineExecutions\\DeExecutionBankDetails.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-13T08:37:03.896Z"}, {"$type": "Document", "DocumentIndex": 132, "Title": "CreditApplicationServiceTests.Details.CashFlow.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationServiceTests.Details.CashFlow.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationServiceTests.Details.CashFlow.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationServiceTests.Details.CashFlow.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationServiceTests.Details.CashFlow.cs", "ViewState": "AgIAACYAAAAAAAAAAAAwwDgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T16:08:16.245Z"}, {"$type": "Document", "DocumentIndex": 133, "Title": "CheckingRevenueResult.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CheckingRevenueResult.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CheckingRevenueResult.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CheckingRevenueResult.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CheckingRevenueResult.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T15:55:43.987Z"}, {"$type": "Document", "DocumentIndex": 134, "Title": "CashFlowItem.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CashFlowItem.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CashFlowItem.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CashFlowItem.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\CreditApplications\\Models\\CashFlowItem.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T15:54:58.543Z"}, {"$type": "Document", "DocumentIndex": 145, "Title": "DrawApprovalListFilter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\List\\DrawApprovalListFilter.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\List\\DrawApprovalListFilter.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\List\\DrawApprovalListFilter.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DrawApprovals\\Models\\List\\DrawApprovalListFilter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T08:06:08.396Z"}, {"$type": "Document", "DocumentIndex": 135, "Title": "CreditApplicationsListQuery.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Queries\\CreditApplicationsListQuery.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Queries\\CreditApplicationsListQuery.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Queries\\CreditApplicationsListQuery.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Queries\\CreditApplicationsListQuery.cs", "ViewState": "AgIAAAMAAAAAAAAAAAArwBAAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-10T11:57:43.44Z"}, {"$type": "Document", "DocumentIndex": 138, "Title": "GlobalUsings.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\GlobalUsings.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\GlobalUsings.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\GlobalUsings.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\GlobalUsings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-10T15:07:18.15Z"}, {"$type": "Document", "DocumentIndex": 137, "Title": "CreditApplicationsServiceTests.Details.DocumentVerification.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationsServiceTests.Details.DocumentVerification.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationsServiceTests.Details.DocumentVerification.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationsServiceTests.Details.DocumentVerification.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\CreditApplicationsServiceTests.Details.DocumentVerification.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-10T15:07:20.823Z"}, {"$type": "Document", "DocumentIndex": 136, "Title": "DraftServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\DraftServiceTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\DraftServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\DraftServiceTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\DraftServiceTests.cs", "ViewState": "AgIAABUAAAAAAAAAAAAowAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-10T15:07:21.396Z"}, {"$type": "Document", "DocumentIndex": 139, "Title": "AppConfigurationServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\AppConfigurationServiceTests.cs", "RelativeDocumentMoniker": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\AppConfigurationServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\AppConfigurationServiceTests.cs", "RelativeToolTip": "..\\tests\\BlueTape.BackOffice.DecisionEngine.Application.Tests\\Services\\AppConfigurationServiceTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-10T15:07:14.364Z"}, {"$type": "Document", "DocumentIndex": 141, "Title": "LoanServiceConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\Constants\\LoanServiceConstants.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\Constants\\LoanServiceConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\Constants\\LoanServiceConstants.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\Constants\\LoanServiceConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-13T11:10:46.628Z"}, {"$type": "Document", "DocumentIndex": 140, "Title": "BlueTape.BackOffice.DecisionEngine.Api.http", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.http", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.http", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.http", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\BlueTape.BackOffice.DecisionEngine.Api.http", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003502|", "WhenOpened": "2024-11-18T10:50:47.261Z"}, {"$type": "Document", "DocumentIndex": 153, "Title": "DecisionEngineService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DecisionEngine\\DecisionEngineService.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DecisionEngine\\DecisionEngineService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Application\\Services\\DecisionEngine\\DecisionEngineService.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Application\\Services\\DecisionEngine\\DecisionEngineService.cs", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-13T11:05:02.413Z"}, {"$type": "Document", "DocumentIndex": 143, "Title": "ErrorDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Error\\ErrorDto.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Error\\ErrorDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Error\\ErrorDto.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Error\\ErrorDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-18T09:37:56.238Z"}, {"$type": "Document", "DocumentIndex": 142, "Title": "ReviewDrawApprovalRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Requests\\ReviewDrawApprovalRequest.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Requests\\ReviewDrawApprovalRequest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api\\Models\\Requests\\ReviewDrawApprovalRequest.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.Api\\Models\\Requests\\ReviewDrawApprovalRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-18T09:38:07.654Z"}, {"$type": "Document", "DocumentIndex": 144, "Title": "ILoanHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\HttpClients\\ILoanHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\HttpClients\\ILoanHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\HttpClients\\ILoanHttpClient.cs", "RelativeToolTip": "BlueTape.BackOffice.DecisionEngine.DataAccess.External\\LoanApi\\HttpClients\\ILoanHttpClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-18T09:29:05.941Z"}]}]}]}