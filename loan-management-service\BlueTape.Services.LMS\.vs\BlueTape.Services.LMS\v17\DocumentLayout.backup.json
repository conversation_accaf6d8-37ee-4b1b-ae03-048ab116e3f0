{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\overduedetectorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\overduedetectorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\calculatorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\calculatorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\creditcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\creditcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\paymentexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\paymentexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\paymenteligibilitychecker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\paymenteligibilitychecker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\getduebasestrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\getduebasestrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\getduelocstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\getduelocstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\getdueihcstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\getdueihcstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\processduebasestrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\processduebasestrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\processdueihcstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\processdueihcstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\processduelocstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\processduelocstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\qacontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\qacontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\auxiliaryloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\auxiliaryloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}|BlueTape.Services.LMS.DataAccess\\BlueTape.Services.LMS.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.dataaccess\\repositories\\loanrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}|BlueTape.Services.LMS.DataAccess\\BlueTape.Services.LMS.DataAccess.csproj|solutionrelative:bluetape.services.lms.dataaccess\\repositories\\loanrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}|BlueTape.Services.LMS.DataAccess\\BlueTape.Services.LMS.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.dataaccess\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}|BlueTape.Services.LMS.DataAccess\\BlueTape.Services.LMS.DataAccess.csproj|solutionrelative:bluetape.services.lms.dataaccess\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\paymentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\paymentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application.tests\\services\\penaltyservices\\penaltydetectorservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|solutionrelative:bluetape.services.lms.application.tests\\services\\penaltyservices\\penaltydetectorservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application.tests\\services\\overduedetectorservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|solutionrelative:bluetape.services.lms.application.tests\\services\\overduedetectorservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\loanparameterscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\loanparameterscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\loancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\loancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\creditholdscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\creditholdscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\basispointcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\basispointcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\authorizationperiodcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\authorizationperiodcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\autopayloancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\autopayloancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\overdueservices\\latefeereceivableservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\overdueservices\\latefeereceivableservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\abstractions\\services\\loanservices\\iauxiliaryloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\abstractions\\services\\loanservices\\iauxiliaryloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanreceivableservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanreceivableservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanreceivablepaymenttimelineservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanreceivablepaymenttimelineservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\overdueservices\\overduedetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\overdueservices\\overduedetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\penaltyservices\\penaltydetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\penaltyservices\\penaltydetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\senders\\invoicesyncmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\senders\\invoicesyncmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}|BlueTape.Services.ARS.IntegrationTests\\BlueTape.Services.ARS.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.ars.integrationtests\\tests\\base\\arsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}|BlueTape.Services.ARS.IntegrationTests\\BlueTape.Services.ARS.IntegrationTests.csproj|solutionrelative:bluetape.services.ars.integrationtests\\tests\\base\\arsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\loanpayablesdetailsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\loanpayablesdetailsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\loandownpaymentsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\loandownpaymentsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application.tests\\extensions\\loanextensionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|solutionrelative:bluetape.services.lms.application.tests\\extensions\\loanextensionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\extensions\\loanextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\extensions\\loanextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\createloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\createloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\infrastructure\\changepaymentstatusstrategy\\approvepaymentstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\infrastructure\\changepaymentstatusstrategy\\approvepaymentstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\paymentservices\\paymentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\paymentservices\\paymentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\models\\loanreceivables\\loanreceivable.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\models\\loanreceivables\\loanreceivable.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\loanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\loanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application.tests\\services\\createloanservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|solutionrelative:bluetape.services.lms.application.tests\\services\\createloanservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B75391C9-B056-450E-96E8-D3AE980AF7A6}|Functions\\BlueTape.Functions.LMS.OverDueDetector\\BlueTape.Functions.LMS.OverDueDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\bluetape.functions.lms.overduedetector\\bluetape.functions.lms.overduedetector.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{B75391C9-B056-450E-96E8-D3AE980AF7A6}|Functions\\BlueTape.Functions.LMS.OverDueDetector\\BlueTape.Functions.LMS.OverDueDetector.csproj|solutionrelative:functions\\bluetape.functions.lms.overduedetector\\bluetape.functions.lms.overduedetector.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\agingreportscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\agingreportscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{87C3D140-B18E-471A-9801-2BA0EEAD10BC}|BlueTape.Services.LMS.IntegrationTests\\BlueTape.Services.LMS.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.integrationtests\\tests\\base\\lmsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{87C3D140-B18E-471A-9801-2BA0EEAD10BC}|BlueTape.Services.LMS.IntegrationTests\\BlueTape.Services.LMS.IntegrationTests.csproj|solutionrelative:bluetape.services.lms.integrationtests\\tests\\base\\lmsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{87C3D140-B18E-471A-9801-2BA0EEAD10BC}|BlueTape.Services.LMS.IntegrationTests\\BlueTape.Services.LMS.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.integrationtests\\tests\\changereceivableintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{87C3D140-B18E-471A-9801-2BA0EEAD10BC}|BlueTape.Services.LMS.IntegrationTests\\BlueTape.Services.LMS.IntegrationTests.csproj|solutionrelative:bluetape.services.lms.integrationtests\\tests\\changereceivableintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\middlewares\\exceptionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\middlewares\\exceptionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\infra\\modules\\mod-fapp\\main.tf||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\сreditstatusdetectorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\сreditstatusdetectorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\bluetape.functions.lms.creditstatusdetector\\creditstatusdetector.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|solutionrelative:functions\\bluetape.functions.lms.creditstatusdetector\\creditstatusdetector.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\creditservices\\creditservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\creditservices\\creditservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\bluetape.functions.lms.creditstatusdetector\\creditstatusconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|solutionrelative:functions\\bluetape.functions.lms.creditstatusdetector\\creditstatusconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\creditservices\\creditstatusdetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\creditservices\\creditstatusdetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\bluetape.functions.lms.creditstatusdetector\\bluetape.functions.lms.creditstatusdetector.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|solutionrelative:functions\\bluetape.functions.lms.creditstatusdetector\\bluetape.functions.lms.creditstatusdetector.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "OverDueDetectorController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\OverDueDetectorController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\OverDueDetectorController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\OverDueDetectorController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\OverDueDetectorController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T08:00:46.42Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "GetDueIHCStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueIHCStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueIHCStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueIHCStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueIHCStrategy.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAawCAAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T15:50:39.929Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ProcessDueIHCStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueIHCStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueIHCStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueIHCStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueIHCStrategy.cs", "ViewState": "AgIAABcAAAAAAAAAAAAQwB4AAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T15:50:26.368Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "PaymentExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\PaymentExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentExternalService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\PaymentExternalService.cs", "ViewState": "AgIAACAAAAAAAAAAAAAWwC8AAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T12:42:11.082Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "ProcessDueBaseStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueBaseStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueBaseStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueBaseStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueBaseStrategy.cs", "ViewState": "AgIAAD8AAAAAAAAAAAAjwE8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T12:42:11.112Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "PaymentEligibilityChecker.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAawA4AAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T09:11:58.197Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CreditController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CreditController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\CreditController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CreditController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\CreditController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:18:46.491Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CalculatorController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CalculatorController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\CalculatorController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CalculatorController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\CalculatorController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:18:54.336Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ProcessDueLOCStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueLOCStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueLOCStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueLOCStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueLOCStrategy.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAQwCUAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T15:50:58.561Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "GetDueBaseStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueBaseStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueBaseStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueBaseStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueBaseStrategy.cs", "ViewState": "AgIAAJAAAAAAAAAAAAAWwI0AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T16:18:30.736Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "GetDueLOCStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueLOCStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueLOCStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueLOCStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueLOCStrategy.cs", "ViewState": "AgIAABYAAAAAAAAAAAD4vyYAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T07:42:30.29Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "PaymentController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\PaymentController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\PaymentController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\PaymentController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\PaymentController.cs", "ViewState": "AgIAAGcAAAAAAAAAAAAswH8AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T16:09:22.047Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.DataAccess\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.DataAccess\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.DataAccess\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "BlueTape.Services.LMS.DataAccess\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAABEAAAAAAAAAAAAxwB8AAABjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T10:38:06.89Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Properties\\launchSettings.json", "RelativeToolTip": "BlueTape.Services.LMS.API\\Properties\\launchSettings.json", "ViewState": "AgIAABUAAAAAAAAAAAAAwCIAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-08T11:51:13.71Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Program.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Program.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Program.cs", "ViewState": "AgIAAHMAAAAAAAAAAAAkwIUAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:28:22.227Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "PenaltyDetectorServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\PenaltyServices\\PenaltyDetectorServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application.Tests\\Services\\PenaltyServices\\PenaltyDetectorServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\PenaltyServices\\PenaltyDetectorServiceTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application.Tests\\Services\\PenaltyServices\\PenaltyDetectorServiceTests.cs", "ViewState": "AgIAAAUBAAAAAAAAAAAWwCcBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:29:33.565Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "AuxiliaryLoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\AuxiliaryLoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\AuxiliaryLoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\AuxiliaryLoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\AuxiliaryLoanService.cs", "ViewState": "AgIAAEoAAAAAAAAAAAAkwEgAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:31:53.795Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "QaController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\QaController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\QaController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\QaController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\QaController.cs", "ViewState": "AgIAAGkAAAAAAAAAAAAAAIAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T12:29:23.953Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "BasisPointController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\BasisPointController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\BasisPointController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\BasisPointController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\BasisPointController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:18:52.926Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "LoanRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.DataAccess\\Repositories\\LoanRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.DataAccess\\Repositories\\LoanRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.DataAccess\\Repositories\\LoanRepository.cs", "RelativeToolTip": "BlueTape.Services.LMS.DataAccess\\Repositories\\LoanRepository.cs", "ViewState": "AgIAADwAAAAAAAAAAAAEwEIAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T11:05:02.889Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "LoanParametersController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\LoanParametersController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\LoanParametersController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\LoanParametersController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\LoanParametersController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:18:56.844Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "OverDueDetectorServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\OverDueDetectorServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application.Tests\\Services\\OverDueDetectorServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\OverDueDetectorServiceTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application.Tests\\Services\\OverDueDetectorServiceTests.cs", "ViewState": "AgIAAEAAAAAAAAAAAAD4v2AAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:29:05.155Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "AuthorizationPeriodController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AuthorizationPeriodController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AuthorizationPeriodController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AuthorizationPeriodController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AuthorizationPeriodController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:18:52.214Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "CreditHoldsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CreditHoldsController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\CreditHoldsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CreditHoldsController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\CreditHoldsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:18:47.365Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "LoanController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T11:04:09.23Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 28, "Title": "IAuxiliaryLoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Abstractions\\Services\\LoanServices\\IAuxiliaryLoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Abstractions\\Services\\LoanServices\\IAuxiliaryLoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Abstractions\\Services\\LoanServices\\IAuxiliaryLoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Abstractions\\Services\\LoanServices\\IAuxiliaryLoanService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T10:53:13.822Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "AdminController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AdminController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AdminController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AdminController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AdminController.cs", "ViewState": "AgIAAA0DAAAAAAAAAAAcwBYDAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:27:47.591Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "AutopayLoanController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AutopayLoanController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AutopayLoanController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AutopayLoanController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AutopayLoanController.cs", "ViewState": "AgIAADUAAAAAAAAAAAAgwFMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T11:04:12.866Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 29, "Title": "LoanReceivableService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanReceivableService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanReceivableService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanReceivableService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanReceivableService.cs", "ViewState": "AgIAABIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:02:36.51Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "LoanReceivablePaymentTimelineService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanReceivablePaymentTimelineService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanReceivablePaymentTimelineService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanReceivablePaymentTimelineService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanReceivablePaymentTimelineService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:02:35.423Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "LateFeeReceivableService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\OverDueServices\\LateFeeReceivableService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\OverDueServices\\LateFeeReceivableService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\OverDueServices\\LateFeeReceivableService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\OverDueServices\\LateFeeReceivableService.cs", "ViewState": "AgIAAOAAAAAAAAAAAAAswPwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T10:45:51.689Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "OverDueDetectorService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\OverDueServices\\OverDueDetectorService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\OverDueServices\\OverDueDetectorService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\OverDueServices\\OverDueDetectorService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\OverDueServices\\OverDueDetectorService.cs", "ViewState": "AgIAAG0AAAAAAAAAAAD4v3AAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T10:51:16.44Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 32, "Title": "PenaltyDetectorService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PenaltyServices\\PenaltyDetectorService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\PenaltyServices\\PenaltyDetectorService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PenaltyServices\\PenaltyDetectorService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\PenaltyServices\\PenaltyDetectorService.cs", "ViewState": "AgIAAFcAAAAAAAAAAAD4v2sAAABMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:02:02.838Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 34, "Title": "InvoiceSyncMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Senders\\InvoiceSyncMessageSender.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Senders\\InvoiceSyncMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Senders\\InvoiceSyncMessageSender.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Senders\\InvoiceSyncMessageSender.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T10:43:04.269Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 36, "Title": "LoanPayablesDetailsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanPayablesDetailsService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanPayablesDetailsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanPayablesDetailsService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanPayablesDetailsService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T10:42:41.941Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 40, "Title": "CreateLoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "ViewState": "AgIAADUAAAAAAAAAAAAUwEcAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:28:16.328Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 35, "Title": "ArsIntegrationTestsBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "RelativeDocumentMoniker": "BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "RelativeToolTip": "BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "ViewState": "AgIAAEQAAAAAAAAAAAD4v1gAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T12:17:31.613Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 33, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAADkAAAAAAAAAAIA7wEwAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:30:21.213Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 42, "Title": "PaymentService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentServices\\PaymentService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\PaymentServices\\PaymentService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentServices\\PaymentService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\PaymentServices\\PaymentService.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAlwD0AAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T12:15:11.78Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 43, "Title": "LoanReceivable.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Models\\LoanReceivables\\LoanReceivable.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Models\\LoanReceivables\\LoanReceivable.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Models\\LoanReceivables\\LoanReceivable.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Models\\LoanReceivables\\LoanReceivable.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T14:06:54.322Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 39, "Title": "LoanExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Extensions\\LoanExtensions.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Extensions\\LoanExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Extensions\\LoanExtensions.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Extensions\\LoanExtensions.cs", "ViewState": "AgIAAFsAAAAAAAAAAAAkwG8AAABMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T15:26:34.845Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 37, "Title": "LoanDownPaymentsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "ViewState": "AgIAANMAAAAAAAAAAAAAANcAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:33:21.075Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 41, "Title": "ApprovePaymentStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Infrastructure\\ChangePaymentStatusStrategy\\ApprovePaymentStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Infrastructure\\ChangePaymentStatusStrategy\\ApprovePaymentStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Infrastructure\\ChangePaymentStatusStrategy\\ApprovePaymentStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Infrastructure\\ChangePaymentStatusStrategy\\ApprovePaymentStrategy.cs", "ViewState": "AgIAAFgAAAAAAAAAAAAjwG0AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T15:35:49.019Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 38, "Title": "LoanExtensionsTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Extensions\\LoanExtensionsTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application.Tests\\Extensions\\LoanExtensionsTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Extensions\\LoanExtensionsTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application.Tests\\Extensions\\LoanExtensionsTests.cs", "ViewState": "AgIAAJcAAAAAAAAAAAAWwKcAAABkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T16:01:53.557Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 45, "Title": "CreateLoanServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\CreateLoanServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application.Tests\\Services\\CreateLoanServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\CreateLoanServiceTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application.Tests\\Services\\CreateLoanServiceTests.cs", "ViewState": "AgIAACQBAAAAAAAAAAAYwDoBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T10:14:45.525Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "LoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "ViewState": "AgIAABQBAAAAAAAAAIA8wCIBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:23:52.933Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 46, "Title": "BlueTape.Functions.LMS.OverDueDetector.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.OverDueDetector\\BlueTape.Functions.LMS.OverDueDetector.csproj", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.LMS.OverDueDetector\\BlueTape.Functions.LMS.OverDueDetector.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.OverDueDetector\\BlueTape.Functions.LMS.OverDueDetector.csproj", "RelativeToolTip": "Functions\\BlueTape.Functions.LMS.OverDueDetector\\BlueTape.Functions.LMS.OverDueDetector.csproj", "ViewState": "AgIAACEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-03T16:00:27.008Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "AgingReportsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T14:43:20.518Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "ExceptionMiddleware.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Middlewares\\ExceptionMiddleware.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Middlewares\\ExceptionMiddleware.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Middlewares\\ExceptionMiddleware.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Middlewares\\ExceptionMiddleware.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAABxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:27:24.296Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "LmsIntegrationTestsBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.IntegrationTests\\Tests\\Base\\LmsIntegrationTestsBase.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.IntegrationTests\\Tests\\Base\\LmsIntegrationTestsBase.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.IntegrationTests\\Tests\\Base\\LmsIntegrationTestsBase.cs", "RelativeToolTip": "BlueTape.Services.LMS.IntegrationTests\\Tests\\Base\\LmsIntegrationTestsBase.cs", "ViewState": "AgIAAEMAAAAAAAAAAAD4v2kAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T12:17:53.281Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "ChangeReceivableIntegrationTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.IntegrationTests\\Tests\\ChangeReceivableIntegrationTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.IntegrationTests\\Tests\\ChangeReceivableIntegrationTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.IntegrationTests\\Tests\\ChangeReceivableIntegrationTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.IntegrationTests\\Tests\\ChangeReceivableIntegrationTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:35:27.768Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "appsettings.beta.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\appsettings.beta.json", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\appsettings.beta.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\appsettings.beta.json", "RelativeToolTip": "BlueTape.Services.LMS.API\\appsettings.beta.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-02T11:37:33.142Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 50, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\appsettings.json", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\appsettings.json", "RelativeToolTip": "BlueTape.Services.LMS.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-02T11:37:00.494Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 54, "Title": "СreditStatusDetectorController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\СreditStatusDetectorController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\СreditStatusDetectorController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\СreditStatusDetectorController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\СreditStatusDetectorController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:52.393Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "main.tf", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\infra\\modules\\mod-fapp\\main.tf", "RelativeDocumentMoniker": "..\\infra\\modules\\mod-fapp\\main.tf", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\infra\\modules\\mod-fapp\\main.tf", "RelativeToolTip": "..\\infra\\modules\\mod-fapp\\main.tf", "ViewState": "AgIAAAcAAAAAAAAAAABhwCEAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-02T11:16:02.925Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 59, "Title": "BlueTape.Functions.LMS.CreditStatusDetector.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "RelativeToolTip": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-01T13:40:03.416Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "CreditStatusConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusConsumer.cs", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusConsumer.cs", "RelativeToolTip": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusConsumer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:06.704Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "CreditStatusDetector.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusDetector.cs", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusDetector.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusDetector.cs", "RelativeToolTip": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusDetector.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:14.795Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "CreditStatusDetectorService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditStatusDetectorService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditStatusDetectorService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditStatusDetectorService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditStatusDetectorService.cs", "ViewState": "AgIAABoAAAAAAAAAAAAcwCgAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:20.577Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "CreditService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditService.cs", "ViewState": "AgIAALsAAAAAAAAAAAAcwMMAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:29.666Z", "EditorCaption": ""}]}]}]}