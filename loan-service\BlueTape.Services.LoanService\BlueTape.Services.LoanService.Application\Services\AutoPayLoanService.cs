﻿using BlueTape.LS.Domain.Enums;
using BlueTape.Services.LoanService.Application.Abstractions.Services;
using BlueTape.Services.LoanService.Application.Models.Loans;
using BlueTape.Services.LoanService.DataAccess.LMS.Abstractions.ExternalServices;
using BlueTape.Services.LoanService.DataAccess.LMS.Entities.LoanReceivables;
using BlueTape.Services.LoanService.DataAccess.LMS.Entities.Loans;
using BlueTape.Services.LoanService.DataAccess.LMS.Extension;
using BlueTape.Utilities.Extensions;

namespace BlueTape.Services.LoanService.Application.Services
{

    public class AutoPayLoanService(ILoanExternalService loanExternalService) : IAutoPayLoanService
    {
        public async Task<IEnumerable<AutoPayLoan>?> GetUpcoming(DateOnly fromDate, int countDays, CancellationToken ct)
        {
            var loans = await loanExternalService.GetUpcoming(fromDate, countDays, ct);

            if (loans is null)
            {
                return new List<AutoPayLoan>();
            }

            var autoPayLoans = loans.Select(GetAutoPayLoan).Where(autoPay => autoPay.NextPaymentAmount > 0);

            return autoPayLoans;
        }

        private static AutoPayLoan GetAutoPayLoan(LoanEntity loan)
        {
            var processingAmount = loan.Payments.Where(p => p.Status is PaymentStatus.Processing
                                   && p.SubType is PaymentSubType.Repayment).Sum(p => p.Amount);

            var nextPaymentDate = GetNextPaymentDate(loan.LoanReceivables);

            // Calculate if loan has overdue receivables based on actual receivable dates, not loan.IsOverdue flag
            var currentDate = DateOnly.FromDateTime(DateTime.UtcNow);
            var hasOverdueReceivables = loan.LoanReceivables.Any(r =>
                r.ExpectedDate < currentDate && r.IsActiveAndNotPaid());

            return new AutoPayLoan()
            {
                Id = loan.Id,
                IsOverdue = hasOverdueReceivables, // Use calculated overdue status
                DueStatus = GetLoanDueStatus(loan.LoanReceivables, processingAmount),
                OverDueAmount = GetOverDueAmount(loan.LoanReceivables, processingAmount, hasOverdueReceivables),
                NextPaymentAmount = GetNextPaymentAmount(loan.LoanReceivables, processingAmount, nextPaymentDate),
                NextPaymentDate = nextPaymentDate,
                NextPaymentDetails = GetNextPaymentDetails(loan.LoanReceivables, nextPaymentDate)
            };
        }

        private static LoanDueStatus GetLoanDueStatus(IEnumerable<LoanReceivableEntity> receivables, decimal processingAmount)
        {
            var currentDate = DateOnly.FromDateTime(DateTime.UtcNow);
            var loanReceivableEntities = receivables.ToList();

            var pastNotPaidReceivable =
                loanReceivableEntities.Find(r => r.IsActiveAndNotPaid() && r.ExpectedDate <= currentDate);
            var futureNotPaidReceivable =
                loanReceivableEntities.Find(r => r.IsActiveAndNotPaid() && r.ExpectedDate >= currentDate);

            if (pastNotPaidReceivable is null) return futureNotPaidReceivable is null
                ? LoanDueStatus.Paid
                : LoanDueStatus.Pending;
            if (IsReceivableOverdue(currentDate, pastNotPaidReceivable)) return LoanDueStatus.Overdue;
            if (IsReceivablePastDue(currentDate, pastNotPaidReceivable)) return processingAmount == 0
                ? LoanDueStatus.PastDue
                : LoanDueStatus.PastDuePaymentProcessing;

            return LoanDueStatus.Due;
        }

        private static List<NextPaymentDetails> GetNextPaymentDetails(IEnumerable<LoanReceivableEntity> receivables, DateOnly date)
        {
            var listOfNextPaymentDetails = new List<NextPaymentDetails>();
            var receivablesOnDate = receivables
                .Where(r => r.ExpectedDate == date)
                .ToList();

            foreach (var receivableOnDate in receivablesOnDate)
            {
                var sameTypesCount = receivablesOnDate.Count(r => r.Type == receivableOnDate.Type);

                if (sameTypesCount > 1)
                {
                    if (IsListOfNextPaymentDetailsHaveSameType(listOfNextPaymentDetails, receivableOnDate)) continue;

                    var nextPaymentDetailsWithSameTypes = new NextPaymentDetails()
                    {
                        ReceivableType = receivableOnDate.Type,
                        Amount = GetOutstandingAmountFromTheSameReceivableTypes(receivablesOnDate, receivableOnDate),
                        Count = sameTypesCount,
                    };

                    listOfNextPaymentDetails.Add(nextPaymentDetailsWithSameTypes);
                    continue;
                }

                var nextPaymentDetails = new NextPaymentDetails()
                {
                    ReceivableType = receivableOnDate.Type,
                    Amount = GetOutstandingAmount(receivableOnDate),
                    Count = 1,
                };

                listOfNextPaymentDetails.Add(nextPaymentDetails);
            }

            return listOfNextPaymentDetails;
        }

        private static decimal GetOverDueAmount(IEnumerable<LoanReceivableEntity> receivables, decimal processingAmount, bool isOverdue)
        {
            // Calculate overdue amount based on receivables past due date, not just isOverdue flag
            // This matches Node.js logic that checks receivables with expectedDate < today
            decimal overDueAmount = 0;
            var currentDate = DateOnly.FromDateTime(DateTime.UtcNow);

            foreach (var receivable in receivables)
            {
                if (receivable.ExpectedDate < currentDate && receivable.IsActiveAndNotPaid())
                {
                    overDueAmount += receivable.ExpectedAmount - receivable.PaidAmount + receivable.AdjustAmount;
                }
            }

            var overdueAmountWithoutProcessingAmount = overDueAmount - processingAmount;
            return overdueAmountWithoutProcessingAmount > 0 ? overdueAmountWithoutProcessingAmount : 0;
        }

        private static decimal GetNextPaymentAmount(IEnumerable<LoanReceivableEntity> receivables, decimal processingAmount, DateOnly date)
        {
            decimal amount = 0;
            var currentDate = DateOnly.FromDateTime(DateTime.UtcNow);

            // Calculate overdue amount first (past due receivables)
            var overdueAmount = 0m;
            foreach (var receivable in receivables)
            {
                if (receivable.ExpectedDate < currentDate && receivable.IsActiveAndNotPaid())
                {
                    overdueAmount += receivable.ExpectedAmount - receivable.PaidAmount + receivable.AdjustAmount;
                }
            }

            // If there are overdue amounts and processing amount doesn't cover them, return overdue amount
            if (overdueAmount > 0 && processingAmount < overdueAmount)
            {
                return Math.Max(overdueAmount - processingAmount, 0);
            }

            // Otherwise, calculate next payment amount (future receivables up to next payment date)
            foreach (var receivable in receivables)
            {
                if (receivable.ExpectedDate <= date && receivable.IsActiveAndNotPaid())
                {
                    amount += receivable.ExpectedAmount - receivable.PaidAmount + receivable.AdjustAmount;
                }
            }
            return Math.Max(amount - processingAmount, 0);
        }

        private static DateOnly GetNextPaymentDate(IEnumerable<LoanReceivableEntity> receivables)
        {
            var loanReceivableEntities = receivables.ToList();
            var nextReceivable = loanReceivableEntities.Find(x =>
                x.ExpectedDate >= DateOnly.FromDateTime(DateTime.UtcNow) && x.IsActiveAndNotPaid());

            return nextReceivable?.ExpectedDate ?? loanReceivableEntities[^1].ExpectedDate;
        }

        private static decimal GetOutstandingAmount(LoanReceivableEntity loanReceivable)
        {
            return loanReceivable.ExpectedAmount - loanReceivable.PaidAmount + loanReceivable.AdjustAmount;
        }

        private static decimal GetOutstandingAmountFromTheSameReceivableTypes(IEnumerable<LoanReceivableEntity> loanReceivables, LoanReceivableEntity loanReceivable)
        {
            return loanReceivables
                .Where(r => r.Type == loanReceivable.Type)
                .Sum(r => r.ExpectedAmount - r.PaidAmount + r.AdjustAmount);
        }

        private static bool IsListOfNextPaymentDetailsHaveSameType(List<NextPaymentDetails> listOfNextPaymentDetails,
            LoanReceivableEntity loanReceivable)
        {
            return listOfNextPaymentDetails.Exists(r => r.ReceivableType == loanReceivable.Type);
        }

        private static bool IsReceivableOverdue(DateOnly currentDate, LoanReceivableEntity receivable)
        {
            return currentDate > receivable.ExpectedDate.AddBusinessDays(3);
        }

        private static bool IsReceivablePastDue(DateOnly currentDate, LoanReceivableEntity receivable)
        {
            return currentDate > receivable.ExpectedDate;
        }
    }
}
